<?php
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
// include ('../include/pdf_exporter.php');
include ('../include/email.php');
require '../ex_ba_sp/library/tcpdf/tcpdf.php';
require '../ex_ba_sp/library/fpdi/fpdi.php';
require_once 'helper.php';
require_once ('../security_helper.php');
sanitize_global_input();

$email = new Email();
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=3116;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

$page="list_ba.php";

// var spinner = document.getElementById('loader');
if(isset($_POST["create"])) {
    // echo "<pre>";
    // print_r($_POST['file']);
    // echo "</pre>";
    
    // echo "<pre>";
    // print_r($_FILES);
    // echo "</pre>";
    echo "<script>";
    echo "console.log('POST file data:', " . json_encode($_POST['file']) . ");";
    echo "console.log('FILES data:', " . json_encode($_FILES) . ");";
    echo "</script>";
    die("Check browser console for debug information (F12 or right-click > Inspect > Console)");
	// spinner.show();
	$no_ba = isset($_GET["no_ba"]) ? $_GET["no_ba"] : '';
	$norekk = $_POST['no_rek']; 
	if($norekk==''){ 
		echo "<script type='text/javascript'>alert('Isi Rekening terlebih Dahulu');</script>";
        echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =create_invoice_ba.php?no_ba=$no_ba'>";
        exit;
	}
 
	$nofaktur1 = $_POST['no_faktur1'];  
	$warna_plate = $_POST['warna_plate'];  
	// echo $warna_plat_v;
	//  echo $nofaktur1;
	//  echo $warna_plate;
 
	// if ($warna_plate == "HITAM"){
	// 	if($nofaktur1!='010' && $nofaktur1!='030' || $nofaktur1!='011' && $nofaktur1!='031'){ 
	// 		echo "<script type='text/javascript'>alert('No Pajak Expeditur Plat Hitam dengan awalan 010 atau 030');</script>";
	// 		echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =create_invoice_ba.php?no_ba=$no_ba'>";
	// 		exit;
	// 	} 
	// }
	// if ($warna_plate == "KUNING"){ 
	// 	if($nofaktur1!='080' && $nofaktur1!='081'){ 
	// 		echo "<script type='text/javascript'>alert('No Pajak Expeditur Plat Kuning dengan awalan 080');</script>";
	// 		echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =create_invoice_ba.php?no_ba=$no_ba'>";
	// 		exit;
	// 	} 
	// }

	
	//exit();
 
	
	// $sql_ba_invoice = "
		// INSERT INTO EX_BA_INVOICE (
			// NO_BA,
			// NO_FAKTUR_PAJAK,
			// TGL_FAKTUR_PAJAK,
			// STATUS_BA_INVOICE,
			// LAMPIRAN,
			// CREATED_BY,
			// CREATED_AT
		// ) VALUES (
			// '$no_ba',
			// '$no_faktur',
			// TO_DATE('$tgl_faktur','YYYY-MM-DD'),
			// 10,
			// '$json_isi_file',
			// $user_id,
			// TO_DATE('" . date("Y-m-d") . "','YYYY-MM-DD')
		// )
	// ";
	// $query_ba_invoice = oci_parse($conn, $sql_ba_invoice);
	// oci_execute($query_ba_invoice);
	
	// $sql_ba = "SELECT NO_INVOICE FROM EX_BA_INVOICE WHERE NO_BA = '$no_ba'";
	// $query_ba = oci_parse($conn, $sql_ba);
	// oci_execute($query_ba);
	
	// $data_ba = array();
	// while($row = oci_fetch_array($query_ba)) {
		// $data_ba = $row;
	// }

	$action = "create_invoice_ba";
	include ('formula_prod.php');

	$query_sql = "SELECT * FROM EX_BA WHERE NO_BA = :no_ba";
	$query = oci_parse($conn, $query_sql);
	oci_bind_by_name($query, ':no_ba', $no_ba);
	$result = oci_execute($query);

	if (!$result) {
	  $error = oci_error($query);
	  error_log("Database error in create_invoice_ba.php (line 96): " . $error['message']);
	  echo "<script type='text/javascript'>alert('Terjadi kesalahan sistem');</script>";
	  echo "<META HTTP-EQUIV='Refresh' Content='0; URL=list_ba.php'>";
	  exit;
	}

	$data_ba = oci_fetch_array($query);
	$no_ba_v = $data_ba['NO_BA'];
	$org_v = $data_ba['ORG'];
	$no_vendor_v = $data_ba['NO_VENDOR'];
	$nama_vendor_v = $data_ba['NAMA_VENDOR'];
	$total_semen_v = $data_ba['KLAIM_SEMEN'];
	$total_ppdks_v = $data_ba['PPDKS'];
	$total_inv_v = $data_ba['TOTAL_INV'];

	if (empty($no_invoice_in)) {
	  error_log("Warning: no_invoice_in is empty in create_invoice_ba.php");
	}

	$query_sql = "SELECT * FROM EX_BA_INVOICE WHERE NO_INVOICE = :no_invoice AND DIPAKAI = 1";
	$query = oci_parse($conn, $query_sql);
	oci_bind_by_name($query, ':no_invoice', $no_invoice_in);
	$result = oci_execute($query);

	if (!$result) {
	  $error = oci_error($query);
	  error_log("Database error in create_invoice_ba.php (line 108): " . $error['message']);
	  echo "<script type='text/javascript'>alert('Terjadi kesalahan sistem');</script>";
	  echo "<META HTTP-EQUIV='Refresh' Content='0; URL=list_ba.php'>";
	  exit;
	}

	$data_invoice_ba = oci_fetch_array($query);

	$no_faktur = $_POST["no_faktur"];
	$tgl_faktur = $_POST["tgl_faktur"];
	$files = isset($_POST["file"]) && is_array($_POST["file"]) ? $_POST["file"] : array();
	$isi_file = array();

	$no = 0;
    $pushNo = 0;
    foreach ($files as $i => $file) {
        $nama = $file['nama'];
        if ($nama != '') {
            if (intval($file['cek']) < 2) {
                $pushNo += 1;
            }
        }
    }

    // if ($pushNo <= 2) {
    //     echo "<script type='text/javascript'>alert('Lengkapi 2 Dokumen terlebih Dahulu');</script>";
    //     echo "<META HTTP-EQUIV ='Refresh' Content ='0; URL =create_invoice_ba.php?no_ba=$no_ba'>";
    //     exit;
    // }

	foreach($files as $i => $file) {
		$lampiran = $file['lampiran'];
		$nama = $file['nama'];
		$data = $file['file'];
		$user = $file['user'];
		$tgl_upload = $file['tanggal'];
		
		$exp_nama = explode(".", $nama);
		$ext = end($exp_nama);
		
		$md5_name = md5($i . date("YmdHis") . $nama) . "." . $ext;
		
		list($type, $data) = explode(';', $data);
		list(, $data)      = explode(',', $data);
		$data = base64_decode($data);
		
		file_put_contents(dirname(__FILE__) . '/lampiran/' . $md5_name, $data);
		
		$isi_file[] = array($lampiran, $tgl_upload, $nama, $md5_name, $user);
	}

	$pdfExporter = new PDFExporter();
	$respPdfKuitansi = $pdfExporter->invoiceKuitansi($no_ba);

    // Menyimpan pdf ke dalam file di CSMS
    $filenameKuitansi = md5(date("YmdHis") . 'kuitansi') . '.pdf';
    $pdf = fopen(dirname(__FILE__) . '/lampiran/' . $filenameKuitansi, 'w');
    fwrite($pdf, $respPdfKuitansi);
    fclose($pdf);

	$field_names = array('FILE_KUITANSI');
	$field_data = array($filenameKuitansi);
	$tablename = "EX_BA_INVOICE";
	$field_id = array('NO_BA', 'DIPAKAI');
	$value_id = array($no_ba, 1);

	$isi_file[] = array('Invoice', date('Y-m-d'), 'Kuitansi - ' . $no_invoice_in . '.pdf', $filenameKuitansi, $_SESSION['user_name']);
    $isi_file[] = array('Ba Rekapitulasi', date('Y-m-d'), $data_ba['FILENAME'], $data_ba['FILENAME'], $_SESSION['user_name']);

	$pakaiSPDendaSama = isset($_POST['sp_denda_check_sama']);
	$pakaiSPDendaBeda = isset($_POST['sp_denda_check_beda']);

	if ($pakaiSPDendaSama) {
		$sp_denda_sama_tgl = implode("-", array_reverse(explode("-", $_POST['sp_denda_sama_tgl']))) . " 00:00:00";

		$field_names_sp_denda = array('PAKAI_SP_DENDA_SAMA', 'SP_DENDA_SAMA_TGL');
		$field_data_sp_denda = array(1, "updtgl_$sp_denda_sama_tgl");

		// Update tanggal sp denda terlebih dahulu supaya tanggalnya muncul saat diprint
		$fungsi->update_safe($conn, $field_names_sp_denda, $field_data_sp_denda, $tablename, $field_id, $value_id);

		// print sp denda
		$resp_pdf_sp_denda_sama = $pdfExporter->invoiceSpDendaTanggalSama($no_ba);

		// Menyimpan pdf ke dalam file di CSMS
		$filename_sp_denda_sama = md5(date("YmdHis") . 'sp denda sama') . "." . 'pdf';
		$pdf = fopen(dirname(__FILE__) . '/lampiran/' . $filename_sp_denda_sama, 'w');
		fwrite($pdf, $resp_pdf_sp_denda_sama);
		fclose($pdf);

		$field_names[] = 'FILE_SP_DENDA_SAMA';
		$field_data[] = $filename_sp_denda_sama;

		$isi_file[] = array('SP Denda Tanggal Sama', date('Y-m-d'), 'SP Denda Tanggal Sama - ' . $no_invoice_in . '.pdf', $filename_sp_denda_sama, $_SESSION['user_name']);
	}

	if ($pakaiSPDendaBeda) {
		$sp_denda_beda_tgl_kontrak = implode("-", array_reverse(explode("-", $_POST['sp_denda_beda_tgl_kontrak']))) . " 00:00:00";
		$sp_denda_beda_tgl_terbit = implode("-", array_reverse(explode("-", $_POST['sp_denda_beda_tgl_terbit']))) . " 00:00:00";
		$sp_denda_beda_tgl_baroa = implode("-", array_reverse(explode("-", $_POST['sp_denda_beda_tgl_baroa']))) . " 00:00:00";

		$field_names_sp_denda = array('PAKAI_SP_DENDA_BEDA', 'SP_DENDA_BEDA_TGL_KONTRAK', 'SP_DENDA_BEDA_TGL_TERBIT', 'SP_DENDA_BEDA_TGL_BAROA');
		$field_data_sp_denda = array(1, "updtgl_$sp_denda_beda_tgl_kontrak", "updtgl_$sp_denda_beda_tgl_terbit", "updtgl_$sp_denda_beda_tgl_baroa");

		// Update tanggal sp denda terlebih dahulu supaya tanggalnya muncul saat diprint
		$fungsi->update_safe($conn, $field_names_sp_denda, $field_data_sp_denda, $tablename, $field_id, $value_id);

		// print sp denda
		$resp_pdf_sp_denda_beda = $pdfExporter->invoiceSpDendaTanggalBeda($no_ba);

		// Menyimpan pdf ke dalam file di CSMS
		$filename_sp_denda_beda = md5(date("YmdHis") . 'sp denda beda') . "." . 'pdf';
		$pdf = fopen(dirname(__FILE__) . '/lampiran/' . $filename_sp_denda_beda, 'w');
		fwrite($pdf, $resp_pdf_sp_denda_beda);
		fclose($pdf);

		$field_names[] = 'FILE_SP_DENDA_BEDA';
		$field_data[] = $filename_sp_denda_beda;

		$isi_file[] = array('SP Denda Tanggal Berbeda', date('Y-m-d'), 'SP Denda Tanggal Berbeda - ' . $no_invoice_in . '.pdf', $filename_sp_denda_beda, $_SESSION['user_name']);
	}

	$json_isi_file = json_encode($isi_file);
	$field_names[] = 'LAMPIRAN';
	$field_data[] = $json_isi_file;

	$fungsi->update_safe($conn, $field_names, $field_data, $tablename, $field_id, $value_id);

	//sendEmail
	$user_approval_id = $data_ba['ID_USER_APPROVAL'];
	if (empty($user_approval_id) || !is_numeric($user_approval_id)) {
	  error_log("Warning: Invalid ID_USER_APPROVAL in create_invoice_ba.php: " . $user_approval_id);
	  $mailTo = '';
	} else {
	  $sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = :user_id";
	  $query = oci_parse($conn, $sql);
	  oci_bind_by_name($query, ':user_id', $user_approval_id);
	  $result = oci_execute($query);

	  if (!$result) {
	    $error = oci_error($query);
	    error_log("Database error in create_invoice_ba.php (email query): " . $error['message']);
	    $mailTo = '';
	  } else {
	    $row = oci_fetch_assoc($query);
	    $mailTo = isset($row['ALAMAT_EMAIL']) ? $row['ALAMAT_EMAIL'] : '';
	  }
	}
	$mailCc = '';
	$email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
	<div align=\"center\">
	<thead>
	<tr class=\"quote\">
	<td ><strong>&nbsp;&nbsp;No.</strong></td>
	<td align=\"center\"><strong>ORG</strong></td>
	<td align=\"center\"><strong>BA REKAPITULASI</strong></td>
	<td align=\"center\"><strong>EKSPEDITUR</strong></td>
	<td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
	<td align=\"center\"><strong>KLAIM SEMEN</strong></td>
	<td align=\"center\"><strong>PDPKS</strong></td>
	<td align=\"center\"><strong>TOTAL</strong></td>
	<td align=\"center\"><strong>STATUS</strong></td>
	</tr>
	</thead>
	<tbody>";

	$email_content_table .= " 
	<td align=\"center\">1</td>
	<td align=\"center\">".$org_v."</td>       
	<td align=\"center\">".$no_ba_v."</td>
	<td align=\"center\">".$no_vendor_v."</td>
	<td align=\"center\">".$nama_vendor_v."</td>
	<td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
	<td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
	<td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
	<td align=\"center\">Open</td>
	</tr>";
    
    $email->sendMail($mailTo, $mailCc, 'Notifikasi Approve Dokumen Invoice', $no_invoice_in, 'Mohon untuk ditindaklanjuti pengajuan Dokumen tsb.', $email_content_table);
	//end sendEmail
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<strong>Pesan!</strong>
		<br>
		<br>
		<div class="alert alert-warning" role="alert"><?=$show_ket?></div>
		<a href="javascript:popUp('print_invoice_ba.php?no_ba=<?=$no_ba?>')" style="margin-right: 16px;">&lt;&lt;&nbsp;&nbsp;Cetak&nbsp;&nbsp;&gt;&gt;</a>
		<a href="<?=$habis?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
	</div>
</div>
<?
exit;
}

// $mp_coics=$fungsi->getComin($conn,$user_org);
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
   $orgIn= $user_org;
// }



$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$no_ba = isset($_GET["no_ba"]) ? $_GET["no_ba"] : '';
if (empty($no_ba)) {
  echo "<script type='text/javascript'>alert('Parameter no_ba tidak valid');</script>";
  echo "<META HTTP-EQUIV='Refresh' Content='0; URL=list_ba.php'>";
  exit;
}

$no_ba = trim($no_ba);
if (!preg_match('/^[A-Za-z0-9\-\/]+$/', $no_ba)) {
  echo "<script type='text/javascript'>alert('Format no_ba tidak valid');</script>";
  echo "<META HTTP-EQUIV='Refresh' Content='0; URL=list_ba.php'>";
  exit;
}

$sql_ba = "SELECT EX_BA.*, to_char(TGL_BA,'YYYY-MM-DD') as TGL_BA_FORMATTED, to_char(TGL_BA,'DD-MM-YYYY') as TGL_INVOICE1 FROM EX_BA WHERE NO_BA = :no_ba";
$query_ba = oci_parse($conn, $sql_ba);
oci_bind_by_name($query_ba, ':no_ba', $no_ba);
$result = oci_execute($query_ba);

if (!$result) {
  $error = oci_error($query_ba);
  error_log("Database error in create_invoice_ba.php: " . $error['message']);
  echo "<script type='text/javascript'>alert('Terjadi kesalahan sistem');</script>";
  echo "<META HTTP-EQUIV='Refresh' Content='0; URL=list_ba.php'>";
  exit;
}

$data_ba = array();
while($row = oci_fetch_array($query_ba)) {
	$data_ba = $row;
}

// $sql = "
// 	SELECT
// 		A.*,
// 		B.CREATE_BY,
// 		B.PIC_GUDANG
// 	FROM
// 		(
// 			SELECT
// 				EX_TRANS_HDR.*,
// 				to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,
// 				to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,
// 				to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,
// 				to_char(TANGGAL_KIRIM,'YYYYMMDD') as TANGGAL_KIRIMF
// 			FROM
// 				EX_TRANS_HDR
// 			WHERE
// 				DELETE_MARK = '0'
// 				AND NO_BA = '$no_ba'
// 			ORDER BY
// 				PLANT,
// 				SAL_DISTRIK,
// 				KODE_KECAMATAN,
// 				KODE_PRODUK,
// 				TANGGAL_KIRIM ASC
// 		) A
// 		LEFT JOIN (
// 			SELECT
// 				m1.NO_SPJ,
// 				m1.CREATE_BY,
// 				m1.PIC_GUDANG
// 			FROM
// 				EX_INPUTCLAIM_SEMEN m1
// 				LEFT JOIN EX_INPUTCLAIM_SEMEN m2 ON (m1.NO_SPJ = m2.NO_SPJ AND m1.id < m2.id)
// 			WHERE
// 				m2.id IS NULL
// 				and m1.DELETE_MARK = '0'
// 				AND m1.STATUS = 'ELOG'
// 		) B ON (A.NO_SHP_TRN = B.NO_SPJ)
// ";
 $sql = "SELECT * FROM EX_BA A JOIN EX_TRANS_HDR B ON A.NO_BA = B.NO_BA WHERE B.NO_BA = :no_ba ORDER BY B.NO_SHP_TRN asc";
//  $sql= "SELECT
// 		EX_BA.ID,
// 		EX_BA.NO_BA,
// 		EX_BA.NO_VENDOR,
// 		EX_BA.TOTAL_INV,
// 		EX_BA.PAJAK_INV,
// 		EX_BA.NAMA_VENDOR,
// 		EX_BA.KLAIM_KTG,
// 		EX_BA.KLAIM_SEMEN,
// 		EX_BA.PDPKS,
// 		EX_BA.PDPKK,
// 		EX_BA.DELETE_MARK,
// 		EX_BA.ORG,
// 		EX_BA.TOTAL_INVOICE,
// 		EX_BA.TGL_BA,
// 		EX_BA.STATUS_BA,
// 		EX_BA.FILENAME,
// 		EX_BA.ALASAN_REJECT,
// 		EX_BA.ID_USER_APPROVAL,
// 		EX_TRANS_HDR.NO_INVOICE,
// 		SUM(EX_TRANS_HDR.QTY_KTG_RUSAK) AS QTY_KTG_RUSAK,
// 		SUM(EX_TRANS_HDR.QTY_SEMEN_RUSAK) AS QTY_SEMEN_RUSAK,
// 		SUM(EX_TRANS_HDR.QTY_SHP) AS QTY_SHP,
// 		SUM(EX_TRANS_HDR.TOTAL_KTG_RUSAK) AS TOTAL_KTG_RUSAK,
// 		SUM(EX_TRANS_HDR.TOTAL_KTG_REZAK) AS TOTAL_KTG_REZAK,
// 		SUM(EX_TRANS_HDR.TOTAL_SEMEN_RUSAK) AS TOTAL_SEMEN_RUSAK,
// 		SUM(EX_TRANS_HDR.TOTAL_KLAIM_KTG) AS TOTAL_KLAIM_KTG,
// 		SUM(EX_TRANS_HDR.TOTAL_KLAIM_SEMEN) AS TOTAL_KLAIM_SEMEN,
// 		to_char( EX_BA.TGL_BA, 'DD-MM-YYYY' ) AS TGL_INVOICE1 
// 	FROM
// 		EX_BA
// 		JOIN EX_TRANS_HDR ON EX_BA.NO_BA = EX_TRANS_HDR.NO_BA
// 	WHERE EX_BA.NO_BA = '$no_ba' 
// 	GROUP BY EX_BA.ID,
// 		EX_BA.NO_BA,
// 		EX_BA.NO_VENDOR,
// 		EX_BA.TOTAL_INV,
// 		EX_BA.PAJAK_INV,
// 		EX_BA.NAMA_VENDOR,
// 		EX_BA.KLAIM_KTG,
// 		EX_BA.KLAIM_SEMEN,
// 		EX_BA.PDPKS,
// 		EX_BA.PDPKK,
// 		EX_BA.DELETE_MARK,
// 		EX_BA.ORG,
// 		EX_BA.TOTAL_INVOICE,
// 		EX_BA.TGL_BA,
// 		EX_BA.STATUS_BA,
// 		EX_BA.FILENAME,
// 		EX_BA.ALASAN_REJECT,
// 		EX_BA.ID_USER_APPROVAL,
// 		EX_TRANS_HDR.NO_INVOICE,
// 		EX_TRANS_HDR.NO_SHP_TRN
// 	ORDER BY
// 	EX_TRANS_HDR.NO_SHP_TRN DESC";
	// echo $sql;
$query = oci_parse($conn, $sql);
oci_bind_by_name($query, ':no_ba', $no_ba);
$result = oci_execute($query);

if (!$result) {
  $error = oci_error($query);
  error_log("Database error in create_invoice_ba.php (main query): " . $error['message']);
  echo "<script type='text/javascript'>alert('Terjadi kesalahan sistem');</script>";
  echo "<META HTTP-EQUIV='Refresh' Content='0; URL=list_ba.php'>";
  exit;
}
while($row = oci_fetch_array($query)) {
	$no_ba_v[]=$row[NO_BA];
	$no_invoice_v[]=$row[NO_INVOICE];
	$tgl_invoice_v[]=$row[TANGGAL_INVOICE];
	$no_invoice_ex_v=$row[NO_INV_VENDOR];
	$spj_v[]=$row[NO_SHP_TRN];
	$spjmd[]=$row[NO_SHP_TRN2];
	$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
	$tgl_datang_v[]=$row[TANGGAL_DATANG];
	$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR];
	$produk_v[]=$row[KODE_PRODUK];
	$nama_produk_v[]=$row[NAMA_PRODUK];
	$shp_trn_v[]=$row[NO_SHP_TRN];
	$plant_v[]=$row[PLANT]; 
	$nama_plant_v[]=$row[NAMA_PLANT]; 
	$warna_plat_v=$row[WARNA_PLAT];
	$tipe_trukd=$row[VEHICLE_TYPE];                
	$nama_vendor_v=$row[NAMA_VENDOR]; 
	$vendor_v=$row[VENDOR];
	$TANGGAL_KIRIMFgg=trim($row[TANGGAL_KIRIMF]);
	$nmmkoqq=$row[ORG];
	$tahunKIRIMFgg=substr(trim($row[TANGGAL_KIRIMF]),0,4);
	$tgl_kirim_sort[]=$row[TANGGAL_KIRIM1];
	$lampiran[]=$row[EVIDENCE_POD1];
            $lampiran2[]=$row[EVIDENCE_POD2];
            $flag_POD[]=$row[FLAG_POD]; 
			$geofence_pod[]=$row[GEOFENCE_POD];
	$tarif_cost[]=doubleval($row[TARIF_COST]);
	$pajak_v[]=$row[PAJAK_INV];
	$total_klaim_v[]=$row[TOTAL_INV];
	$keterangan_pod[]=$row[KETERANGAN_POD];
	
	//perubahan untuk costum kereta dijadikan plat hitam
	if($tipe_trukd=='205' && $vendor_v=='0000410095' ){
		$warna_plat_v='HITAM';
	}
	$sal_dis_v[]=$row[SAL_DISTRIK]; 
	$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
	$sold_to_v[]=$row[SOLD_TO];
	$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
	$ship_to_v[]=$row[SHIP_TO];
	$qty_v[]=$row[QTY_SHP];
	$um_rez=$row[UM_REZ];
	if($um_rez > 1) {
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ]/1000;
	} else {
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ];
	}
	$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
	$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
	$qty_pdpks_v[]=$row[PDPKS];
	$qty_tot_klaim_ktg_v[]=$row[TOTAL_KLAIM_KTG];
	$qty_tot_klaim_smn_v[]=$row[TOTAL_KLAIM_SEMEN];
	$id_v[]=$row[ID];  
	$no_pol_v[]=$row[NO_POL];  
	$shp_cost_v[]=$row[SHP_COST];  
	$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
	$no_pajak_ex=$row[NO_PAJAK_EX];  		
	$kel=$row[KELOMPOK_TRANSAKSI];  		
	$inco=$row[INCO];  		
	$nama_kapal=$row[NAMA_KAPAL];  		
	$kode_kecamatan[]=$row[KODE_KECAMATAN];
	$tipe_truk[]=$row[VEHICLE_TYPE];
	if($row[PIC_GUDANG] != '') {
		$createby[]=$row[PIC_GUDANG];
	} else {
		if($row[CREATE_BY] != '') {
			$createby[]=$row[CREATE_BY];
		} else{
			$createby[]=$vendor;
		}
	}
	
	//Penyederhanaan Lap.OA @t 6 Feb 2012
	$item_no = trim($row[KODE_PRODUK]);                
	#Klaim Kantong
	$arr_klaim_kantong[$item_no]+=($row[TOTAL_KTG_RUSAK]+$row[TOTAL_KTG_REZAK]);  
	#Klaim Semen
	$arr_klaim_semen[$item_no]+=($row[TOTAL_SEMEN_RUSAK]+$row[PDPKS]);                
	$arr_nama_material[$item_no] = $row[NAMA_PRODUK];
	
	#Qty.
	$arr_qty_klaim_kantong[$item_no]+=$row[QTY_KTG_RUSAK];
	$arr_qty_klaim_semen[$item_no]+=$row[QTY_SEMEN_RUSAK];                
}
$total=count($shp_trn_v);



// $query = "
//   SELECT
//     TUB.*,
//     EUP.STATUS_AKTIF
//   FROM
//     TB_USER_BOOKING TUB
//   LEFT JOIN EX_BA_USER_APPROVAL EUP ON
//     EUP.ID_USER = TUB.ID
//   WHERE
//     TUB.DELETE_MARK = 0
//     AND EUP.STATUS_AKTIF = 1
//     AND ( TUB.VENDOR_NAME LIKE '$vendor'
//       OR TUB.VENDOR LIKE '$vendor' )
//   ORDER BY
//     TUB.NAMA ASC
// ";
// $query = oci_parse($conn, $query);
// oci_execute($query);

// $data_user_approval = array();
// while ($row = oci_fetch_array($query)) {
//   $data_user_approval[] = $row;
// }

$data_user_approval = get_pejabat_eks_manual();


$filhpajak='';
if ($warna_plat_v == "HITAM"){
//@liyantanto
$filhpajak="javascript:kodepajak(no_faktur1);";
}
if ($warna_plat_v == "KUNING"){
//@liyantanto
$filhpajak="javascript:kodepajakkuning(no_faktur1);";
}
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Create Invoice</th>
</tr></table>
</div>

<div align="center">
<table width="95%" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA Rekapitulasi </span></th>
</tr>
</table>
</div> 
<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
		<thead>
			<tr class="quote">
				<td align="center"><strong>No.</strong></td>
				<td align="center"><strong>No BA</strong></td>
				<td align="center"><strong>Tgl BA</strong></td>
				<!-- <td align="center"><strong>Tgl SPJ</strong></td> -->
				<td align="center"><strong>Area LT</strong></td>
				<td align="center"><strong>No SPJ</strong></td>
				<td align="center"><strong>SPJ MD</strong></td>
				<td align="center"><strong>Nopol</strong></td>
				<td align="center"><strong>Plat</strong></td>
				<td align="center"><strong>Produk</strong></td>
				<td align="center"><strong>Distributor</strong></td>
				<!-- <td align="center"><strong>K. KTG</strong></td> -->
				<td align="center"><strong>K. SMN</strong></td>
				<td align="center"><strong>PPDKS</strong></td>
				<!-- <td align="center"><strong>TOT K. KTG</strong></td> -->
				<td align="center"><strong>TOT K. SEMEN</strong></td>
				<td align="center"><strong>Qty</strong></td>
				<td align="center"><strong>Tarif</strong></td>
				<td align="center"><strong>Sub Jumlah</strong></td>
				<!-- <td align="center"><strong>PPN</strong></td> -->
				<!-- <td align="center"><strong>Jumlah</strong></td> -->
				<td align="center"><strong>Tanggal Datang</strong></td>
				<td align="center"><strong>Tanggal Bongkar</strong></td>
				<td align="center"><strong>POD</strong></td>
				<td align="center"><strong>USER POD</strong></td>
				<td align="center"><strong>Geofence</strong></td>
				<td align="center"><strong>Lampiran 1</strong></td>
				<td align="center"><strong>Lampiran 2</strong></td>
			</tr>
		</thead>
		<tbody>
			<?
				$total_qty_kantong_rusak_v = 0;
				$total_qty_semen_rusak_v = 0;
				$total_pdpks_v = 0;
				$total_tot_klaim_ktg_v = 0;
				$total_tot_klaim_smn_v = 0;
				$total_qty_v = 0;
				$total_shp_cost_v = 0;
				$total_pajak_v = 0;
				$grand_total = 0;
				for($i = 0; $i < $total; $i++) {
					$total_qty_kantong_rusak_v += $qty_kantong_rusak_v[$i];
					$total_qty_semen_rusak_v += $qty_semen_rusak_v[$i];
					$total_pdpks_v += $qty_pdpks_v[$i];
					$total_tot_klaim_ktg_v += $qty_tot_klaim_ktg_v[$i];
					$total_tot_klaim_smn_v += $qty_tot_klaim_smn_v[$i];
					$total_qty_v += $qty_v[$i];
					$total_tarif_cost += $tarif_cost[$i];
					$total_shp_cost_v += $shp_cost_v[$i];
					$total_pajak_v = $pajak_v[$i];
					$grand_total = $total_klaim_v[$i]+$pajak_v[$i];
			?>
			<tr class="row<? echo ($i % 2) == 0 ? 0 : 1; ?>">
				<td align="center"><?=($i + 1)?></td>
				<td align="center"><a href="javascript:popUp('detail_ba.php?no_ba=<?=$no_ba_v[$i]?>')"><? echo $no_ba_v[$i]; ?></a></td>
				<td align="center"><?=$data_ba[TGL_INVOICE1]?></td>
				<!-- <td align="center"><?=$tgl_kirim_sort[$i]?></td> -->
				<td align="center"><?=$sal_dis_v[$i]?></td>
				<td align="center"><?=$spj_v[$i]?></td>
				<td align="center"><?=$spjmd[$i]?></td>
				<td align="center"><?=$no_pol_v[$i]?></td>
				<td align="center"><?=$warna_plat_v?></td>
				<td align="center"><? echo $nama_produk_v[$i]; ?></td>
				<td align="center"><? echo $sold_to_v[$i]." / ".$nama_sold_to_v[$i]; ?></td>
				<!-- <td align="center"><?=number_format($qty_kantong_rusak_v[$i], 0, ",", ".")?></td>	 -->
				<td align="center"><?=number_format($qty_semen_rusak_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_pdpks_v[$i], 0, ",", ".")?></td>
				<!-- <td align="center"><?=number_format($qty_tot_klaim_ktg_v[$i], 0, ",", ".")?></td> -->
				<td align="center"><?=number_format($qty_tot_klaim_smn_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($tarif_cost[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($shp_cost_v[$i], 2, ",", ".")?></td>
				<!-- <td align="center"><? echo number_format($pajak_v[$i],2,",","."); ?></td> -->
        		<!-- <td align="center"><? echo number_format($total_klaim_v[$i]+$pajak_v[$i],2,",","."); ?></td> -->
				<td align="center"><? echo $tgl_datang_v[$i]; ?></td>
				<td align="center"><? echo $tgl_bongkar_v[$i]; ?></td>
				<td align="center"><?=$flag_POD[$i]?></td> 
				<td align="center"><?=$keterangan_pod[$i]?></td> 
				<? if ($geofence_pod[$i] != ''){?>
				<td align="center"><a href="javascript:popUp('https://www.google.com/search?q=<?php echo $geofence_pod[$i] ?>')"><?php echo $geofence_pod[$i] ?></a></td>
				<? } else { ?>
				<td align="center">GEOFENCE POD</td>
				<? } ?> 
				<td align="center"><a href="javascript:popUp('<?php echo $lampiran[$i] ?>')">Lampiran SPJ</a></td>
				<td align="center"><a href="javascript:popUp('<?php echo $lampiran2[$i] ?>')">Lampiran TTD SPJ</a></td>
			</tr>
			<? } ?>
		</tbody>
		<tfoot>
			<tr class="quote">
				<td align="center" colspan="9"><strong>TOTAL</strong></td>
				<td align="center"></td> <!-- <strong><?=number_format($total_qty_kantong_rusak_v, 0, ",", ".")?></strong> -->
				<td align="center"><strong><?=number_format($total_qty_semen_rusak_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_pdpks_v, 0, ",", ".")?></strong></td>
				<!-- <td align="center"><strong><?=number_format($total_tot_klaim_ktg_v, 0, ",", ".")?></strong></td> -->
				<td align="center"><strong><?=number_format($total_tot_klaim_smn_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_qty_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_tarif_cost, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_shp_cost_v, 2, ",", ".")?></strong></td>
				<!-- <td align="center"><strong><?=number_format($total_pajak_v, 2, ",", ".")?></strong></td> -->
				<!-- <td align="center"><strong><?=number_format($grand_total, 2, ",", ".")?></strong></td> -->
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
			</tr>
		</tfoot>
	</table>
</div>
<br/>
<br/>
<form method="post" id="form_invoice" action="" onsubmit="return validateForm()">
	<input name="no_ba" type="hidden" value="<?=$no_ba?>">
	<input name="warna_plate" id="warna_plate" type="hidden" value="<?=$warna_plat_v?>">
	<input name="no_vendor" id="no_vendor" type="hidden" value="<?=$vendor_v?>">
	<input name="nama_vendor" type="hidden" value="<?=$nama_vendor_v?>">
	<input name="total_tarif" type="hidden" value="<?=$total_tarif_cost?>">
	<input name="total_shp_in" type="hidden" value="<?=$total_shp_cost_v?>">
	<input name="total_ktg_in" type="hidden" value="0">
	<input name="total_semen_in" type="hidden" value="<?=$total_qty_semen_rusak_v?>">
	<input name="org_in" type="hidden" value="<?=$nmmkoqq?>">
	<div align="center">
		<table cellspacing="0" cellpadding="0" border="0" width="95%">
			<tbody>
				<tr>
					<td style="padding: 4px; font-weight: bold;">Tanggal</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="date" id="tgl_faktur" name="tgl_faktur" required="required" style="width: 248px;" onchange="updateNoFaktur()">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 150px; padding: 4px; font-weight: bold;">No Faktur Pajak</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3" id="no_faktur_container">
						<input type="text" name="no_faktur1" required="required" style="width: 30px;" maxlength="2"  onBlur="javascript:IsNumeric(this,'2');">.
						<input type="text" name="no_faktur2" required="required" style="width: 30px;" maxlength="2" onBlur="javascript:IsNumeric(this,'2');">.
						<input type="text" name="no_faktur3" required="required" style="width: 30px;" maxlength="2" onBlur="javascript:IsNumeric(this,'2');">.
						<input type="text" name="no_faktur4" required="required" style="width: 50px;" maxlength="3" onBlur="javascript:IsNumeric(this,'3');"> -
						<input type="text" name="no_faktur5" required="required" style="width: 80px;" maxlength="8" onBlur="javascript:IsNumeric(this,'8');">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 150px; padding: 4px; font-weight: bold;">No Kwitansi Expeditur</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" name="no_kwitansi_vendor" required="required" style="width: 248px;" maxlength="40">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 150px; padding: 4px; font-weight: bold;">No Invoice Expeditur</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" name="no_invoice_vendor" style="width: 248px;" maxlength="40"> *boleh kosong
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px; font-weight: bold;">No Rekening</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="width: 40px; padding: 4px;">
						<input type="text" id="bvtyp" name="bvtyp"  value="" style="width: 100%;" readonly="true">
					</td>
					<td style="width: 200px; padding: 4px;">
						<input type="text" id="nama_bank" name="nama_bank" value="" style="width: 100%;" readonly="true">
					</td>
					<td style="width: 100px; padding: 4px;">
						<input type="text" id="no_rek" name="no_rek" value="" style="width: 100%;" readonly="true">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px;">&nbsp;</td>
					<td style="padding: 4px;">&nbsp;</td>
					<td style="padding: 4px;" colspan="2">
						<input type="text" id="cabang_bank" name="cabang_bank" value="" style="width: 100%;" readonly="true">
					</td>
					<td style="padding: 4px;">
						<input name="cari_rek" type="button" class="button" id="cari_rek" value="..." style="width: 40px;" onClick="find_rek()">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 150px; padding: 4px; font-weight: bold;">SP Denda Faktur Pajak</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="checkbox" name="sp_denda_check" id="sp_denda_check">
					</td>
					<td></td>
				</tr>
				<tr id="sp_denda_container">
					<td style="width: 150px; padding: 4px; font-weight: bold; vertical-align: top;">Tanggal SP Denda</td>
					<td style="width: 16px; padding: 4px; font-weight: bold; vertical-align: top;">:</td>
					<td style="padding: 4px; padding-bottom: 10px;" colspan="3" required>
						<div>
							<input type="checkbox" name="sp_denda_check_sama" id="sp_denda_check_sama">
							<label for="sp_denda_check_sama">Surat Pernyataan Denda</label>
						</div>

						<div style="margin-top: 5px;">
							<input type="hidden" name="sp_denda_sama_tgl" id="sp_denda_sama_tgl">
							<input type="checkbox" name="sp_denda_check_beda" id="sp_denda_check_beda">
							<label for="sp_denda_check_beda">Surat Pernyataan Denda Beda Tanggal</label>
						</div>
					</td>
					<td></td>
				</tr>
				<tr class="sp_denda_section_beda">
					<td style="width: 150px; padding: 4px; font-weight: bold; vertical-align: top;">Tanggal FP (Sesuai Kontrak)</td>
					<td style="width: 16px; padding: 4px; font-weight: bold; vertical-align: top;">:</td>
					<td style="padding: 4px; padding-bottom: 10px;" colspan="3" required>
						<div>
							<input type="date" name="sp_denda_beda_tgl_kontrak" id="sp_denda_beda_tgl_kontrak" style="width: 248px;">
						</div>
					</td>
					<td></td>
				</tr>
				<tr class="sp_denda_section_beda">
					<td style="width: 150px; padding: 4px; font-weight: bold; vertical-align: top;">Tanggal FP (Terbit)</td>
					<td style="width: 16px; padding: 4px; font-weight: bold; vertical-align: top;">:</td>
					<td style="padding: 4px; padding-bottom: 10px;" colspan="3" required>
						<div>
							<input type="date" name="sp_denda_beda_tgl_terbit" id="sp_denda_beda_tgl_terbit" style="width: 248px;">
						</div>
					</td>
					<td></td>
				</tr>
				<tr class="sp_denda_section_beda">
					<td style="width: 150px; padding: 4px; font-weight: bold; vertical-align: top;">Tanggal BAROA</td>
					<td style="width: 16px; padding: 4px; font-weight: bold; vertical-align: top;">:</td>
					<td style="padding: 4px; padding-bottom: 10px;" colspan="3" required>
						<div>
							<?php
								$minDate = new DateTime('now');
								$minDate->modify('-1 month');
							?>
							<input type="date" name="sp_denda_beda_tgl_baroa" id="sp_denda_beda_tgl_baroa" style="width: 248px;" readonly>
						</div>
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 150px; padding: 4px; font-weight: bold;">User Approval</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td colspan="2">
						<select name="id_user_approval" style="width: 100%;" required>
							<option value="">--- Pilih ---</option>
							<?php foreach ($data_user_approval as $user): ?>
								<option value="<?=$user['ID']?>"><?=$user['NAMA_LENGKAP']?></option>
							<?php endforeach ?>
						</select>
					</td>
				</tr>
				<tr>
					<td style="padding: 4px; font-weight: bold;">Lampiran</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="file" style="width: 248px;" onchange="tambahFile(this);" accept="image/gif, image/jpeg, image/png, application/pdf" multiple>
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>

		<br/>
		<br/>

		<table cellspacing="0" cellpadding="0" border="0" width="95%" align="center">
			<tbody>
				<tr>
					<td style="width: 500px;">
						<table align="center" class="adminlist">
							<thead>
								<tr class="quote">
									<td align="center" style="width: 40px;"><strong>NO</strong></td>
									<td align="center" style="width: 120px;"><strong>Nama Lampiran</strong></td>
									<td align="center" style="width: 100px;"><strong>Tanggal Upload</strong></td>
									<td align="center" style="width: 120px;"><strong>Lampiran</strong></td>
									<td align="center" style="width: 200px;"><strong>Submit By</strong></td>
									<td align="center" style="width: 60px;"><strong>Aksi</strong></td>
								</tr>
							</thead>
							<tbody id="isiLampiran">
								<?php
								$lampiran = array("Faktur Pajak", "Kontrak/PO");
								$bulan = array(null, "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC");
								?>

								<?php 
								$i = 0;
								foreach($lampiran as $l) {
									$required = ($l == 'Faktur Pajak') ? 'required' : '';
									$asterisk = ($l == 'Faktur Pajak') ? "<span style='color:red;'>*</span>" : "";
									echo "
										<tr class='row-file'>
											<td style='display:none;' ><input type='hidden'  name='file[$i][cek]' value='$i' style='width: 100%;' maxlength='40'></td>
											<td style='display:none;' ><input type='hidden'  id='file$i' name='file[$i][file]' style='width: 100%;' maxlength='40'></td>
											<td style='display:none;' ><input type='hidden' name='file[$i][lampiran]' value='".htmlspecialchars($l)."' style='width: 100%;' maxlength='40'></td>
											<td  style='display:none;'><input type='hidden' id='namafile$i' name='file[$i][nama]'  style='width: 100%;' maxlength='40'></td>
											<td  style='display:none;' ><input type='hidden' name='file[$i][tanggal]' value='".date("Y")."-".date("m")."-".date("d")."' style='width: 100%;' maxlength='40'></td>
											<td  style='display:none;' ><input type='hidden' name='file[$i][user]' value='".$_SESSION['user_name']."' style='width: 100%;' maxlength='40'></td>
										
											<td align='center' class='nomor-file'>".($i+1)."</td>
											<td align='center'>".htmlspecialchars($l).$asterisk."</td>
											<td align='center'>".date("Y")."-".$bulan[date("m")]."-".date("d")."</td>
											<td align='center'><input type='file' nomor='$i' onchange='editFile(this);'  name='file[$i][files]' style='width: 248px;' accept='image/gif, image/jpeg, image/png, application/pdf' $required></a></td>
											<td align='center'>".$_SESSION['user_name']."</td>
											<td align='center'><p id='preview$i'></p></td>
										</tr>
									";
									$i++;
								}
								?>
							</tbody>
						</table>
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>
		
		<br/>
		<br/>

		<table cellspacing="0" cellpadding="0" border="0" width="95%" align="center">
			<tbody>
				<tr>
					<td style="width: 500px;">
						<input name="create" value="ok" hidden />
						<button type="submit" id="btn_create_inv" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Create Invoice</button>
						<button type='button' id='kembali' style="font-size: 14px; text-decoration: none; margin-left: 4px; padding: 4px; background-color: #ff0000; color: #fff; border: 1px solid #000; border-radius: 4px;">Back</a>
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>
		
		<br/>
		<br/>

		<table cellspacing="0" cellpadding="0" border="0" width="95%" align="center">
			<tbody>
				<tr>
					<td style="width: 500px;">
						<i style="color:red;">Note: Ukuran File Lampiran Max. 2 MB</i>
					</td>
				</tr>
			</tbody>
		</table>


	</div>
</form>


<form id="back-form" action="list_ba.php" method="POST">
	<input type="hidden" name="cari" value="cari" />
	<input type="hidden" name="tanggal_mulai" value="<?= $_SESSION['tgl_mulai_inv'] ?>" />
	<input type="hidden" name="tanggal_selesai" value="<?= $_SESSION['tgl_end_inv'] ?>" />
	<input type="hidden" name="no_invoice" value="<?= $_SESSION['no_inv'] ?>" />
</form>
<div id="loader"></div>
<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
<script src="../include/jquery.min.js"></script>
<script type="text/javascript">
	//We write the table and the div to hide the content out, so older browsers won't see it
	obj=document.getElementById("tunggu_ya");
	obj.style.display = "none";
	obj_tampil=document.getElementById("halaman_tampil");
	obj_tampil.style.display = "inline";
	$("#kembali").click(function(event) {
		event.preventDefault();
		$("#back-form").submit();
	});
	
	var monthNames = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
	var hari_ini = new Date();
	var tgl = String(hari_ini.getDate()).padStart(2, '0');
	var bln = hari_ini.getMonth();
	var thn = hari_ini.getFullYear();
	
	var jumlahFile = 0;
	var indexFile = 2;
	
	function tambahFile(file) {
		if((file.value).length > 0) {
			
			const fileSize = file.files[0].size / 1024 / 1024; // in MiB
			  if (fileSize > 2) {
				alert('File size max 2 MB');
				file.value="";
				 return false;
			  } 
			var reader = new FileReader();
			var user = "<?php echo $_SESSION['user_name'] ?>";
			reader.readAsDataURL(file.files[0]);
			reader.onload = function () {
				$("#isiLampiran").append(
					"<tr class='row-file'>" +
						"<td align='center' class='nomor-file'></td>" +
						"<td align='center'>" +
							"<input type='text' name='file["+indexFile+"][lampiran]' required='required' style='width: 100%;' maxlength='40'>" +
							"<input type='hidden' name='file["+indexFile+"][nama]' style='display: none;' value=\""+file.files[0].name+"\">" +
							"<input type='hidden' name='file["+indexFile+"][file]' style='display: none;' value=\""+reader.result+"\">" +
						"</td>" +
						"<td align='center'><input type='hidden' name='file["+indexFile+"][tanggal]' style='display: none;' value='"+thn+'-'+String(bln+1).padStart(2, '0')+'-'+tgl+"'>"+tgl+"-"+monthNames[bln]+"-"+thn+"</td>" +
						"<td align='center'><a href='javascript:void(0);' data-base64=\""+reader.result+"\" data-name=\""+file.files[0].name+"\" onclick='previewFile(this);'>"+file.files[0].name+"</a></td>" +
						"<td align='center'><input type='text' name='file["+indexFile+"][user]' value=\""+user+"\" style='width: 100%;' readonly='readonly'>" +
						"<td align='center' style='font-size: 18px;'><a href='javascript:void(0);' onclick='kurangiFile(this);' style='cursor: pointer; text-decoration: none;'>&times;</a></td>" +
					"</tr>"
				);
				file.value = "";
				jumlahFile++;
				indexFile++;
				refreshNomor();
			}
			reader.onerror = function (error) {
				alert('Error: ', error);
			}
		}
	}

	function editFile(file) { 
			const fileSize = file.files[0].size / 1024 / 1024; // in MiB
			  if (fileSize > 2) {
				alert('File size max 2 MB');
				file.value="";
				 return false;
			  } 
			var reader = new FileReader();
			var user = "<?php echo $_SESSION['user_name'] ?>";
			reader.readAsDataURL(file.files[0]);
			console.log(file);
			var cobas = $(file).attr("nomor"); 
			$("#namafile"+cobas).val(file.files[0].name) 
			reader.onload = function () {
				$("#file"+cobas).val(reader.result)
				$("#preview"+cobas).append("<a href='javascript:void(0);' style='cursor: pointer; text-decoration: none;' data-base64=\""+reader.result+"\" data-name=\""+file.files[0].name+"\" onclick='previewFile(this);'>View</a>")
			};
	
}
	
	function kurangiFile(elm) {
		if(confirm("Hapus file?")) {
			elm.parentNode.closest('.row-file').remove();
			jumlahFile--;
			refreshNomor();
		}
	}
	
	function refreshNomor() {
		var elmNomor = document.getElementsByClassName('nomor-file');
		for (var i=0; i < elmNomor.length; i++) {
			elmNomor[i].innerHTML = i + 1;
		}
	}
	
	function kodepajak(val){
		console.log(val.value)
		if(val.value!='010'||val.value!='030' || val.value!='040' || val.value!='011'||val.value!='031'|| val.value!='041'){
			alert("No Pajak Expeditur Plat Hitam  dengan awalan 010 atau 030 atau 040");
			val.value='';
			val.select();
			val.focus();
			return false;
		}else{
	  //return true;
		}

	}
	
	function kodepajakkuning(val){
		if(val.value!='080' || val.value!='081'){
			alert("No Pajak Expeditur Plat Kuning dengan awalan 080 atau 081");
			val.value='';
			val.select();
			val.focus();
			return false;
		}else{
	  //return true;
		}

	}
	
	
function IsNumeric(obj,panjang)
   //  check for valid numeric strings
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   if (strString.length != panjang){
     alert("Isi dengan Angka " + panjang + " digit..");
   obj.value="";
   return false;
  } else {
     //  test strString consists of valid characters listed above
     for (i = 0; i < strString.length; i++)
      {
      strChar = strString.charAt(i);
      if (strValidChars.indexOf(strChar) == -1)
       {
       alert("Hanya Masukkan Angka 0-9 dengan " + panjang + " digit..");
       obj.value="";
       return false;
       }
      }
   }
   }
	
	
	
	function previewFile(elm) {
		var w = window.open('about:blank');
 
		// FireFox seems to require a setTimeout for this to work.
		setTimeout(() => {
		  w.document.body.appendChild(w.document.createElement('iframe')).src = $(elm).data("base64");
		  w.document.body.style.margin = 0;
		  w.document.getElementsByTagName("iframe")[0].style.width = '100%';
		  w.document.getElementsByTagName("iframe")[0].style.height = '100%';
		  w.document.getElementsByTagName("iframe")[0].style.border = 0;
		}, 10);
	}
	
	function validateForm() {
	  // if (jumlahFile == 0) {
		// alert("Upload file terlebih dahulu.");
		// return false;
	  // }
		// $("#btn_create_inv").text("Loading...");
    	// $("#btn_create_inv").attr("disabled", "disabled");
	}
	
	function find_rek() {
		var no_vendor = document.getElementById("no_vendor");
		var strURL="cari_rek.php?no_vendor="+no_vendor.value;
		popUp(strURL);
	}

	var sp_denda_check = $('#sp_denda_check');
	var sp_denda_container = $('#sp_denda_container');

	var sp_denda_check_sama = $('#sp_denda_check_sama');
	var sp_denda_check_beda = $('#sp_denda_check_beda');
	var sp_denda_section_beda = $('.sp_denda_section_beda');

	var sp_denda_sama_tgl = $('#sp_denda_sama_tgl');
	var sp_denda_beda_tgl_kontrak = $('#sp_denda_beda_tgl_kontrak');
	var sp_denda_beda_tgl_terbit = $('#sp_denda_beda_tgl_terbit');
	var sp_denda_beda_tgl_baroa = $('#sp_denda_beda_tgl_baroa');

	function triggerSpDendaSamaChange() {
		if (sp_denda_check_sama.is(':checked')) {
			sp_denda_sama_tgl.val('<?=$data_ba['TGL_BA_FORMATTED']?>');
		} else {
			sp_denda_sama_tgl.val('');
		}
	}

	function triggerSpDendaBedaChange() {
		if (sp_denda_check_beda.is(':checked')) {
			sp_denda_section_beda.show();
			sp_denda_beda_tgl_kontrak.prop('required', true);
			sp_denda_beda_tgl_terbit.prop('required', true);
			sp_denda_beda_tgl_baroa.val('<?=$data_ba['TGL_BA_FORMATTED']?>');
		} else {
			sp_denda_section_beda.hide();
			sp_denda_beda_tgl_kontrak.val('').prop('required', false);
			sp_denda_beda_tgl_terbit.val('').prop('required', false);
			sp_denda_beda_tgl_baroa.val('').prop('required', false);
		}
	}

	function triggerSpDendaChange() {
		if (sp_denda_check.is(':checked')) {
			sp_denda_container.show(); 
		} else {
			sp_denda_container.hide();
			sp_denda_check_sama.prop('checked', false);
			sp_denda_check_beda.prop('checked', false);
		}

		triggerSpDendaSamaChange();
		triggerSpDendaBedaChange();
	}

	sp_denda_check.on('change', triggerSpDendaChange);
	sp_denda_check_sama.on('change', triggerSpDendaSamaChange);
	sp_denda_check_beda.on('change', triggerSpDendaBedaChange);

	triggerSpDendaChange();


	// perubahan hilmi

	function updateNoFaktur() {
        const tglFakturInput = document.getElementById('tgl_faktur').value;
        const noFakturContainer = document.getElementById('no_faktur_container');

        if (!tglFakturInput) return; // Jika tanggal belum diisi, hentikan

        const selectedYear = new Date(tglFakturInput).getFullYear();

        if (selectedYear === 2024) {
            noFakturContainer.innerHTML = `
                <input type="text" name="no_faktur1" required="required" style="width: 50px;" maxlength="3" onBlur="javascript:IsNumeric(this,'3');">.
                <input type="text" name="no_faktur2" required="required" style="width: 50px;" maxlength="3" onBlur="javascript:IsNumeric(this,'3');">-
                <input type="text" name="no_faktur3" required="required" style="width: 50px;" maxlength="2" onBlur="javascript:IsNumeric(this,'2');">.
                <input type="text" name="no_faktur4" required="required" style="width: 80px;" maxlength="8" onBlur="javascript:IsNumeric(this,'8');">
            `;
        } else if (selectedYear === 2025) {
            noFakturContainer.innerHTML = `
                <input type="text" name="no_faktur1" required="required" style="width: 30px;" maxlength="2" onBlur="javascript:IsNumeric(this,'2');">.
                <input type="text" name="no_faktur2" required="required" style="width: 30px;" maxlength="2" onBlur="javascript:IsNumeric(this,'2');">.
                <input type="text" name="no_faktur3" required="required" style="width: 30px;" maxlength="2" onBlur="javascript:IsNumeric(this,'2');">.
                <input type="text" name="no_faktur4" required="required" style="width: 50px;" maxlength="3" onBlur="javascript:IsNumeric(this,'3');">-
                <input type="text" name="no_faktur5" required="required" style="width: 80px;" maxlength="8" onBlur="javascript:IsNumeric(this,'8');">
            `;
        } else {
            noFakturContainer.innerHTML = `<span style="color: red;">Tahun tidak didukung!</span>`;
        }
    }

    // Fungsi validasi numeric
    function IsNumeric(input, maxLength) {
        const value = input.value;
        if (!/^\d+$/.test(value) || value.length > maxLength) {
            alert('Input harus berupa angka dengan panjang maksimal ' + maxLength + ' karakter.');
            input.value = ''; // Reset jika input tidak valid
        }
    }
</script>

</body>
</html>
